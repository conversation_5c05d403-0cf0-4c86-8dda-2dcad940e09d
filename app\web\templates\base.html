<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}YAZAKI Component Processing System{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        
        .header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .status-badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-online {
            background: #27ae60;
            color: white;
        }
        
        .status-offline {
            background: #e74c3c;
            color: white;
        }
        
        .file-drop-zone {
            border: 3px dashed #bdc3c7;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }
        
        .file-drop-zone.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            padding: 12px 24px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            border: none;
        }
        
        .suggestion-box {
            background: #e8f4fd;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .column-card {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .column-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }
        
        .column-card.project-column {
            border-color: #27ae60;
            background: #f8fff8;
        }
        
        .progress-bar-custom {
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #27ae60);
            transition: width 0.3s;
        }
        
        .confidence-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .confidence-high { background: #27ae60; color: white; }
        .confidence-medium { background: #f39c12; color: white; }
        .confidence-low { background: #95a5a6; color: white; }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Status Indicator -->
    <div class="status-indicator">
        <div id="backend-status" class="status-badge status-offline">
            <i class="fas fa-circle me-1"></i>Vérification...
        </div>
    </div>
    
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="main-container">
                    <div class="header">
                        <h1><i class="fas fa-industry me-3"></i>YAZAKI - Component Processing</h1>
                        <div class="subtitle">Professional data enrichment system</div>
                    </div>
                    
                    <div class="content">
                        <!-- Messages Flash -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Vérification du status du backend
        async function checkBackendStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                const statusElement = document.getElementById('backend-status');
                if (data.success && data.backend_available) {
                    statusElement.className = 'status-badge status-online';
                    statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>System OK';
                } else {
                    statusElement.className = 'status-badge status-offline';
                    statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>System Unavailable';
                }
            } catch (error) {
                const statusElement = document.getElementById('backend-status');
                statusElement.className = 'status-badge status-offline';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>System Error';
            }
        }
        
        // Vérifier le status au loading et toutes les 30 secondes
        document.addEventListener('DOMContentLoaded', function() {
            checkBackendStatus();
            setInterval(checkBackendStatus, 30000);
        });
        
        // Fonctions utilitaires
        function showError(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const content = document.querySelector('.content');
            content.insertBefore(alertDiv, content.firstChild);
        }
        
        function showSuccess(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const content = document.querySelector('.content');
            content.insertBefore(alertDiv, content.firstChild);
        }
        
        function getConfidenceClass(confidence) {
            if (confidence > 0.7) return 'confidence-high';
            if (confidence > 0.5) return 'confidence-medium';
            return 'confidence-low';
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
