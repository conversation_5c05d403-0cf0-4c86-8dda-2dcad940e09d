{% extends "base.html" %}

{% block title %}Results - YAZAKI Component Processing System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-download me-2"></i>Result Files
                </h4>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <p class="text-muted mb-1">
                            Download the result files generated by processing
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>Use "Preview" to view content without downloading
                        </small>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success btn-sm" onclick="downloadAll()" id="download-all-btn" style="display: none;">
                            <i class="fas fa-download me-1"></i>Download All
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshResults()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
                
                <!-- Zone de loading -->
                <div id="loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading des files...</p>
                </div>
                
                <!-- Liste des files -->
                <div id="files-container" class="d-none">
                    <div id="files-list"></div>
                </div>
                
                <!-- Message si aucun file -->
                <div id="no-files" class="text-center py-4 d-none">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5>Aucun file de résultat disponible</h5>
                    <p class="text-muted">
                        Traitez d'abord un file pour voir les results ici.
                    </p>
                    <a href="{{ url_for('upload_enhanced') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Traiter un file
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Informations sur les types de files -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Types de Files Générés</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded" style="border-color: #28a745 !important; background-color: #f8fff8;">
                            <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                            <h5 class="text-success">📊 File Principal</h5>
                            <h6><strong>Update_YYYY-MM-DD.xlsx</strong></h6>
                            <small class="text-muted">
                                <strong>VOS DATA avec informations Master BOM</strong><br>
                                Contient vos data d'origine enrichies avec Status, Description, Price selon le project sélectionné
                            </small>
                            <div class="mt-2">
                                <span class="badge bg-success">FILE PRINCIPAL À DOWNLOAD</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-database fa-2x text-secondary mb-2"></i>
                            <h6>Master BOM Mis à Jour</h6>
                            <small class="text-muted">
                                Master BOM complet avec nouvelles data intégrées (file de référence)
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                            <h6>Résumé de Processing</h6>
                            <small class="text-muted">
                                Statistiques détaillées du processing effectué (CSV)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let filesData = [];
    
    async function loadResults() {
        try {
            document.getElementById('loading').classList.remove('d-none');
            document.getElementById('files-container').classList.add('d-none');
            document.getElementById('no-files').classList.add('d-none');
            
            const response = await fetch('/api/list-outputs');
            const data = await response.json();
            
            document.getElementById('loading').classList.add('d-none');
            
            if (data.success && data.files && data.files.length > 0) {
                filesData = data.files;
                displayFiles(data.files);
                document.getElementById('files-container').classList.remove('d-none');
            } else {
                document.getElementById('no-files').classList.remove('d-none');
            }
            
        } catch (error) {
            document.getElementById('loading').classList.add('d-none');
            document.getElementById('no-files').classList.remove('d-none');
            showError('Error lors du loading des files: ' + error.message);
        }
    }
    
    function displayFiles(files) {
        const container = document.getElementById('files-list');

        if (files.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Aucun file disponible</p>';
            document.getElementById('download-all-btn').style.display = 'none';
            return;
        }

        // Organiser les files par catégories
        const mainFiles = [];
        const masterBomFiles = [];
        const summaryFiles = [];
        const otherFiles = [];

        files.forEach(file => {
            const name = file.filename.toLowerCase();
            if (name.startsWith('update_') && name.endsWith('.xlsx')) {
                mainFiles.push(file);
            } else if (name.includes('master_bom')) {
                masterBomFiles.push(file);
            } else if (name.includes('summary') || name.includes('processing')) {
                summaryFiles.push(file);
            } else {
                otherFiles.push(file);
            }
        });

        let html = '';

        // Section Files Principaux
        if (mainFiles.length > 0) {
            html += `
                <div class="mb-4">
                    <h6 class="text-success mb-3">
                        <i class="fas fa-star me-2"></i>Files Principaux (Vos data enrichies)
                    </h6>
                    <div class="list-group">
            `;

            mainFiles.forEach(file => {
                html += createFileItem(file, true);
            });

            html += '</div></div>';
        }

        // Section Files de Référence
        if (masterBomFiles.length > 0) {
            html += `
                <div class="mb-4">
                    <h6 class="text-secondary mb-3">
                        <i class="fas fa-database me-2"></i>Files de Référence (Master BOM)
                    </h6>
                    <div class="list-group">
            `;

            masterBomFiles.forEach(file => {
                html += createFileItem(file, false);
            });

            html += '</div></div>';
        }

        // Section Rapports
        if (summaryFiles.length > 0) {
            html += `
                <div class="mb-4">
                    <h6 class="text-info mb-3">
                        <i class="fas fa-chart-line me-2"></i>Rapports de Processing
                    </h6>
                    <div class="list-group">
            `;

            summaryFiles.forEach(file => {
                html += createFileItem(file, false);
            });

            html += '</div></div>';
        }

        // Section Autres Files
        if (otherFiles.length > 0) {
            html += `
                <div class="mb-4">
                    <h6 class="text-muted mb-3">
                        <i class="fas fa-folder me-2"></i>Autres Files
                    </h6>
                    <div class="list-group">
            `;

            otherFiles.forEach(file => {
                html += createFileItem(file, false);
            });

            html += '</div></div>';
        }

        container.innerHTML = html;

        // Afficher le bouton "Tout download" s'il y a plusieurs files
        if (files.length > 1) {
            document.getElementById('download-all-btn').style.display = 'inline-block';
        }
    }

    function createFileItem(file, isMainFile) {
        const fileSize = formatFileSize(file.size);
        const fileDate = new Date(file.modified * 1000).toLocaleString('fr-FR');
        const fileIcon = getFileIcon(file.filename);
        const description = getFileDescription(file.filename);

        const itemClass = isMainFile ? 'list-group-item border-success bg-light' : 'list-group-item';
        const badge = isMainFile ? '<span class="badge bg-success ms-2">FILE PRINCIPAL</span>' : '';

        return `
            <div class="${itemClass}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="d-flex align-items-center">
                        <i class="${fileIcon} fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-1">${file.filename}${badge}</h6>
                            <p class="mb-1 text-muted small">${description}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${fileDate} •
                                <i class="fas fa-hdd me-1"></i>${fileSize}
                            </small>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-info btn-sm" onclick="previewFile('${file.filename}')" title="Voir le contenu sans download">
                            <i class="fas fa-eye me-1"></i>Preview
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="showDistribution('${file.filename}')" title="Voir la distribution des statuss">
                            <i class="fas fa-chart-pie me-1"></i>Distribution
                        </button>
                        <a href="${file.frontend_download_url}" class="btn ${isMainFile ? 'btn-success' : 'btn-outline-success'} btn-sm" title="Download le file">
                            <i class="fas fa-download me-1"></i>${isMainFile ? 'Download Principal' : 'Download'}
                        </a>
                    </div>
                </div>
            </div>
        `;
    }
    
    function getFileIcon(filename) {
        const name = filename.toLowerCase();
        if (name.startsWith('update_') && name.endsWith('.xlsx')) {
            return 'fas fa-star text-warning'; // File principal - étoile dorée
        } else if (name.includes('master_bom')) {
            return 'fas fa-database text-secondary';
        } else if (name.includes('summary') || name.includes('processing')) {
            return 'fas fa-chart-line text-info';
        } else if (name.includes('excluded') || name.includes('clean')) {
            return 'fas fa-file-alt text-warning';
        } else {
            return 'fas fa-file text-secondary';
        }
    }

    function getFileDescription(filename) {
        const name = filename.toLowerCase();
        if (name.startsWith('update_') && name.endsWith('.xlsx')) {
            return '📊 VOS DATA enrichies avec informations Master BOM (Status, Description, Price) selon le project sélectionné';
        } else if (name.includes('master_bom')) {
            return '🗄️ Master BOM complet mis à jour avec nouvelles data (file de référence)';
        } else if (name.includes('summary') || name.includes('processing')) {
            return '📈 Statistiques détaillées du processing (count de lines, durée, etc.)';
        } else if (name.includes('excluded') || name.includes('clean')) {
            return '⚠️ Data qui n\'ont pas pu être traitées (lines exclues)';
        } else {
            return '📄 File de résultat du processing';
        }
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getStatusClass(status) {
        const statusLower = status.toLowerCase();
        if (statusLower.includes('active') || statusLower.includes('actif')) return 'bg-success';
        if (statusLower.includes('inactive') || statusLower.includes('inactif')) return 'bg-danger';
        if (statusLower.includes('pending') || statusLower.includes('attente')) return 'bg-warning';
        if (statusLower.includes('obsolete') || statusLower.includes('obsolète')) return 'bg-secondary';
        if (statusLower.includes('new') || statusLower.includes('nouveau')) return 'bg-info';
        if (statusLower.includes('vide') || statusLower.includes('empty')) return 'bg-light text-dark';
        if (statusLower.includes('rempli') || statusLower.includes('filled')) return 'bg-primary';
        return 'bg-secondary';
    }

    function getStatusColor(status) {
        const statusLower = status.toLowerCase();
        if (statusLower.includes('active') || statusLower.includes('actif')) return 'bg-success';
        if (statusLower.includes('inactive') || statusLower.includes('inactif')) return 'bg-danger';
        if (statusLower.includes('pending') || statusLower.includes('attente')) return 'bg-warning';
        if (statusLower.includes('obsolete') || statusLower.includes('obsolète')) return 'bg-secondary';
        if (statusLower.includes('new') || statusLower.includes('nouveau')) return 'bg-info';
        if (statusLower.includes('vide') || statusLower.includes('empty')) return 'bg-light';
        if (statusLower.includes('rempli') || statusLower.includes('filled')) return 'bg-primary';
        return 'bg-secondary';
    }

    function getChartColor(status) {
        const statusLower = status.toLowerCase();
        if (statusLower.includes('active') || statusLower.includes('actif')) return '#28a745';
        if (statusLower.includes('inactive') || statusLower.includes('inactif')) return '#dc3545';
        if (statusLower.includes('pending') || statusLower.includes('attente')) return '#ffc107';
        if (statusLower.includes('obsolete') || statusLower.includes('obsolète')) return '#6c757d';
        if (statusLower.includes('new') || statusLower.includes('nouveau')) return '#17a2b8';
        if (statusLower.includes('vide') || statusLower.includes('empty')) return '#f8f9fa';
        if (statusLower.includes('rempli') || statusLower.includes('filled')) return '#007bff';
        return '#6c757d';
    }
    
    async function previewFile(filename) {
        try {
            // Afficher un modal de loading
            const previewModal = document.createElement('div');
            previewModal.className = 'modal fade';
            previewModal.id = 'preview-modal';
            previewModal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-eye me-2"></i>Preview : ${filename}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="preview-content">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading de l'preview...</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="/download/${filename}" class="btn btn-success">
                                <i class="fas fa-download me-1"></i>Download ce file
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(previewModal);
            const modal = new bootstrap.Modal(previewModal);
            modal.show();

            // Charger l'preview
            const response = await fetch(`/api/preview/${filename}`);
            const data = await response.json();

            if (data.success && data.preview) {
                const preview = data.preview;
                let html = `
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Informations du file</h6>
                            <ul class="list-unstyled">
                                <li><strong>Nom :</strong> ${preview.filename}</li>
                                <li><strong>Lines :</strong> ${preview.total_rows}</li>
                                <li><strong>Columns :</strong> ${preview.total_columns}</li>
                                <li><strong>Taille :</strong> ${formatFileSize(preview.file_size)}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-columns me-2"></i>Columns disponibles</h6>
                            <div class="d-flex flex-wrap gap-1">
                `;

                preview.columns.forEach(col => {
                    const isImportant = ['PN', 'YAZAKI PN', 'Status', 'Description', 'Price', 'Project'].some(important =>
                        col.toLowerCase().includes(important.toLowerCase())
                    );
                    html += `<span class="badge ${isImportant ? 'bg-primary' : 'bg-secondary'}">${col}</span>`;
                });

                html += `
                            </div>
                        </div>
                    </div>

                    <h6><i class="fas fa-table me-2"></i>Preview des data (5 premières lines)</h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                `;

                preview.columns.forEach(col => {
                    html += `<th>${col}</th>`;
                });

                html += `
                                </tr>
                            </thead>
                            <tbody>
                `;

                preview.sample_data.forEach(row => {
                    html += '<tr>';
                    preview.columns.forEach(col => {
                        const value = row[col] || '';
                        const displayValue = String(value).length > 30 ? String(value).substring(0, 30) + '...' : value;
                        html += `<td title="${value}">${displayValue}</td>`;
                    });
                    html += '</tr>';
                });

                html += `
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note :</strong> Cet preview montre seulement les 5 premières lines.
                        Le file complet contient ${preview.total_rows} lines.
                    </div>
                `;

                document.getElementById('preview-content').innerHTML = html;

            } else {
                document.getElementById('preview-content').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Unable to charger l'preview : ${data.message || 'Error inconnue'}
                    </div>
                `;
            }

            // Nettoyer le modal quand il se ferme
            previewModal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(previewModal);
            });

        } catch (error) {
            console.error('Error preview:', error);
            alert(`Error lors du loading de l'preview: ${error.message}`);
        }
    }

    async function showDistribution(filename) {
        try {
            // Afficher un modal de loading
            const distributionModal = document.createElement('div');
            distributionModal.className = 'modal fade';
            distributionModal.id = 'distribution-modal';
            distributionModal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-pie me-2"></i>Distribution des Statuss : ${filename}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="distribution-content">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Analyse en cours...</span>
                                </div>
                                <p class="mt-2">Analyse de la distribution des statuss...</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-info" onclick="previewFile('${filename}')">
                                <i class="fas fa-eye me-1"></i>Voir Preview
                            </button>
                            <a href="/download/${filename}" class="btn btn-success">
                                <i class="fas fa-download me-1"></i>Download
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(distributionModal);
            const modal = new bootstrap.Modal(distributionModal);
            modal.show();

            // Charger l'analyse de distribution
            const response = await fetch(`/api/distribution/${filename}`);
            const data = await response.json();

            if (data.success && data.distributions) {
                const stats = data.stats;
                const distributions = data.distributions;

                let html = `
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                            <div class="card">
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>File :</strong> ${stats.filename}</li>
                                        <li><strong>Total lines :</strong> ${stats.total_rows.toLocaleString()}</li>
                                        <li><strong>Total columns :</strong> ${stats.total_columns}</li>
                                        <li><strong>Columns analysées :</strong> ${stats.analyzed_columns}</li>
                                        <li><strong>Taille :</strong> ${formatFileSize(stats.file_size)}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-bar me-2"></i>Résumé de l'Analyse</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="text-center">
                                        <div class="row">
                                            <div class="col-4">
                                                <div class="h3 text-primary">${stats.total_rows.toLocaleString()}</div>
                                                <small class="text-muted">Components</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h3 text-success">${stats.analyzed_columns}</div>
                                                <small class="text-muted">Analyses</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h3 text-info">${Object.keys(distributions).length}</div>
                                                <small class="text-muted">Distributions</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Afficher chaque distribution
                Object.keys(distributions).forEach((columnName, index) => {
                    const distribution = distributions[columnName];
                    const totalItems = Object.values(distribution).reduce((sum, item) => sum + item.count, 0);

                    html += `
                        <div class="mb-4">
                            <h6><i class="fas fa-chart-pie me-2"></i>Distribution : ${columnName}</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Status</th>
                                                            <th>Count</th>
                                                            <th>Percentage</th>
                                                            <th>Barre</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                    `;

                    // Trier par count décroissant
                    const sortedEntries = Object.entries(distribution).sort((a, b) => b[1].count - a[1].count);

                    sortedEntries.forEach(([status, data]) => {
                        const statusClass = getStatusClass(status);
                        const barColor = getStatusColor(status);

                        html += `
                            <tr>
                                <td>
                                    <span class="badge ${statusClass}">${status}</span>
                                </td>
                                <td><strong>${data.count.toLocaleString()}</strong></td>
                                <td>${data.percentage}%</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar ${barColor}"
                                             style="width: ${data.percentage}%"
                                             title="${data.count} items (${data.percentage}%)">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <canvas id="chart-${index}" width="200" height="200"></canvas>
                                                <small class="text-muted mt-2 d-block">
                                                    Total: ${totalItems.toLocaleString()} items
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note :</strong> This analysis helps you understand la répartition des statuss
                        dans vos data traitées. Use this information to evaluate la qualité du processing.
                    </div>
                `;

                document.getElementById('distribution-content').innerHTML = html;

                // Créer les graphiques en camembert (si Chart.js est disponible)
                if (typeof Chart !== 'undefined') {
                    Object.keys(distributions).forEach((columnName, index) => {
                        const distribution = distributions[columnName];
                        const canvas = document.getElementById(`chart-${index}`);

                        if (canvas) {
                            const labels = Object.keys(distribution);
                            const data = Object.values(distribution).map(item => item.count);
                            const colors = labels.map(label => getChartColor(label));

                            new Chart(canvas, {
                                type: 'doughnut',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        data: data,
                                        backgroundColor: colors,
                                        borderWidth: 2,
                                        borderColor: '#fff'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            labels: {
                                                boxWidth: 12,
                                                font: {
                                                    size: 10
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    });
                }

            } else {
                document.getElementById('distribution-content').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Unable to charger l'analyse de distribution : ${data.message || 'Error inconnue'}
                    </div>
                `;
            }

            // Nettoyer le modal quand il se ferme
            distributionModal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(distributionModal);
            });

        } catch (error) {
            console.error('Error distribution:', error);
            alert(`Error lors du loading de la distribution: ${error.message}`);
        }
    }
    
    function refreshResults() {
        loadResults();
    }
    
    // Charger les results au démarrage
    document.addEventListener('DOMContentLoaded', function() {
        loadResults();
        
        // Refresh automatiquement toutes les 30 secondes
        setInterval(loadResults, 30000);
    });
    
    // Fonction pour download tous les files
    function downloadAll() {
        if (filesData.length === 0) {
            showError('Aucun file à download');
            return;
        }

        // Demander confirmation
        const mainFiles = filesData.filter(f => f.filename.toLowerCase().startsWith('update_'));
        const otherFiles = filesData.filter(f => !f.filename.toLowerCase().startsWith('update_'));

        let message = `Download ${filesData.length} file(s) ?\n\n`;
        if (mainFiles.length > 0) {
            message += `📊 Files principaux : ${mainFiles.length}\n`;
        }
        if (otherFiles.length > 0) {
            message += `📁 Autres files : ${otherFiles.length}\n`;
        }
        message += '\nLes téléloadings se feront avec un délai de 1 seconde entre chaque file.';

        if (!confirm(message)) {
            return;
        }

        // Désactiver le bouton pendant le téléloading
        const downloadBtn = document.getElementById('download-all-btn');
        const originalText = downloadBtn.innerHTML;
        downloadBtn.disabled = true;

        let downloaded = 0;

        filesData.forEach((file, index) => {
            setTimeout(() => {
                const link = document.createElement('a');
                link.href = file.frontend_download_url;
                link.download = file.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                downloaded++;
                downloadBtn.innerHTML = `<i class="fas fa-download me-1"></i>Téléloading... ${downloaded}/${filesData.length}`;

                // Réactiver le bouton quand tous les téléloadings sont lancés
                if (downloaded === filesData.length) {
                    setTimeout(() => {
                        downloadBtn.disabled = false;
                        downloadBtn.innerHTML = originalText;
                        showSuccess(`${filesData.length} file(s) téléchargé(s)`);
                    }, 1000);
                }

            }, index * 1000); // Délai de 1 seconde entre chaque téléloading
        });
    }
</script>
{% endblock %}
