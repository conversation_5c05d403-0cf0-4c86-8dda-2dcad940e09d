# Component Data Processor - Python Dependencies
# Install with: pip install -r requirements.txt

# Core data processing libraries
pandas>=1.5.0
numpy>=1.21.0

# Excel file handling
openpyxl>=3.0.9
xlsxwriter>=3.0.3

# Web framework dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
pydantic>=2.0.0

# Web interface (Flask - existing)
flask>=2.3.0
werkzeug>=2.3.0
requests>=2.31.0

# Additional utilities
pathlib2>=2.3.6; python_version < '3.4'

# Optional: For enhanced Excel formatting and charts
xlwings>=0.27.0

# Optional: For email functionality (if EMAIL_ENABLED=True in config)
# smtplib is built-in to Python, no external package needed

# Optional: For advanced data validation
cerberus>=1.3.4

# Optional: For progress bars during processing
tqdm>=4.64.0

# Development and testing dependencies (optional)
pytest>=7.0.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0

# Optional: For creating more sophisticated reports
matplotlib>=3.5.0
seaborn>=0.11.0

# Optional: For database connectivity (if Master BOM is in database)
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0  # For PostgreSQL
pymysql>=1.0.0          # For MySQL

# Optional: For configuration management
pyyaml>=6.0
configparser>=5.2.0

# Optional: For logging enhancements
colorlog>=6.6.0

# Optional: For file watching and auto-processing
watchdog>=2.1.0
